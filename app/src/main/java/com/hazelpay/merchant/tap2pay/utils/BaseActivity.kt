package com.hazelpay.merchant.tap2pay.utils

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import androidx.appcompat.app.AppCompatActivity

/**
 * Base activity for activities that need to bind to device services
 * Based on the Landi printer SDK sample code
 */
abstract class BaseActivity : AppCompatActivity() {

    private var deviceService: Any? = null
    private var isServiceBound = false

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            try {
                // Get the device service instance
                deviceService = service
                isServiceBound = true
                onDeviceServiceConnected()
                Log.d("BaseActivity", "Device service connected successfully")
            } catch (e: Exception) {
                Log.e("BaseActivity", "Error connecting to device service", e)
                onDeviceServiceCrash()
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            deviceService = null
            isServiceBound = false
            onDeviceServiceDisconnected()
            Log.d("BaseActivity", "Device service disconnected")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Service binding will be handled in onResume
    }

    override fun onResume() {
        super.onResume()
        bindDeviceService()
    }

    override fun onPause() {
        super.onPause()
        unbindDeviceService()
    }

    /**
     * Bind to the device service
     */
    protected fun bindDeviceService() {
        try {
            if (!isServiceBound) {
                val intent = Intent().apply {
                    // Set the component name for the Landi device service
                    // This may need to be adjusted based on the actual service name in the JAR
                    setClassName("com.landi.device", "com.landi.device.DeviceService")
                }
                
                val bindResult = bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
                if (!bindResult) {
                    Log.w("BaseActivity", "Failed to bind to device service")
                    // Continue without service binding - some functionality may be limited
                }
            }
        } catch (e: Exception) {
            Log.e("BaseActivity", "Exception while binding device service", e)
            // Continue without service binding
        }
    }

    /**
     * Unbind from the device service
     */
    protected fun unbindDeviceService() {
        try {
            if (isServiceBound) {
                unbindService(serviceConnection)
                isServiceBound = false
                deviceService = null
            }
        } catch (e: Exception) {
            Log.e("BaseActivity", "Exception while unbinding device service", e)
        }
    }

    /**
     * Get the device service instance
     */
    protected fun getDeviceService(): Any? = deviceService

    /**
     * Check if device service is bound
     */
    protected fun isDeviceServiceBound(): Boolean = isServiceBound

    /**
     * Called when device service is successfully connected
     * Override in subclasses to handle service connection
     */
    protected open fun onDeviceServiceConnected() {
        // Default implementation - do nothing
    }

    /**
     * Called when device service is disconnected
     * Override in subclasses to handle service disconnection
     */
    protected open fun onDeviceServiceDisconnected() {
        // Default implementation - do nothing
    }

    /**
     * Called when device service crashes
     * Override in subclasses to handle service crashes
     */
    protected open fun onDeviceServiceCrash() {
        // Default implementation - try to rebind
        bindDeviceService()
    }
}
